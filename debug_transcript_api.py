#!/usr/bin/env python3
"""
Debug script to test transcript extraction exactly like the API does
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.youtube_service import YouTubeService
import asyncio
import logging

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def debug_api_transcript_extraction():
    """Debug transcript extraction exactly like the API"""
    
    youtube_url = "https://www.youtube.com/watch?v=Ar4LrKcwh3w&t=412s&ab_channel=A9SPORTS"
    
    print("🔍 Debugging API Transcript Extraction")
    print("=" * 60)
    print(f"🔗 URL: {youtube_url}")
    
    try:
        # Step 1: Initialize YouTube service (exactly like API)
        youtube_service = YouTubeService()
        print("✅ YouTube service initialized")
        
        # Step 2: Extract video ID (exactly like API)
        video_id = youtube_service.extract_video_id(youtube_url)
        print(f"🆔 Extracted video ID: {video_id}")
        
        if not video_id:
            print("❌ Failed to extract video ID")
            return
        
        # Step 3: Get video info (exactly like API)
        print("📹 Getting video info...")
        video_info = await youtube_service.get_video_info(video_id)
        print(f"📊 Video info: {video_info.get('title', 'Unknown')}")
        print(f"⏱️ Duration: {video_info.get('duration', 'Unknown')} seconds")
        
        # Step 4: Try to get transcript (exactly like API)
        print("📝 Attempting to get transcript...")
        try:
            transcript_data = youtube_service.get_transcript(video_id)
            
            if transcript_data:
                print("✅ SUCCESS: Transcript found!")
                print(f"📝 Language: {transcript_data['language']}")
                print(f"🤖 Type: {transcript_data['type']}")
                print(f"📊 Length: {len(transcript_data['text'])} characters")
                print(f"📄 Preview: {transcript_data['text'][:200]}...")
                
                # Test transcript processing
                if transcript_data.get('transcript'):
                    print(f"🔢 Raw transcript entries: {len(transcript_data['transcript'])}")
                    
            else:
                print("❌ No transcript data returned")
                
        except Exception as transcript_error:
            print(f"❌ TRANSCRIPT ERROR: {transcript_error}")
            print(f"🔍 Error type: {type(transcript_error)}")
            
            # Try to get more details about the error
            try:
                from youtube_transcript_api import YouTubeTranscriptApi
                print("🔍 Attempting direct API call...")
                transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
                print(f"📋 Available transcripts: {list(transcript_list)}")
                
                # Try to get any available transcript
                for transcript in transcript_list:
                    print(f"🎯 Found transcript: {transcript.language} ({transcript.language_code}) - Generated: {transcript.is_generated}")
                    
            except Exception as direct_error:
                print(f"❌ Direct API error: {direct_error}")
        
    except Exception as e:
        print(f"❌ GENERAL ERROR: {e}")
        print(f"🔍 Error type: {type(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🏁 Debug completed!")

if __name__ == "__main__":
    asyncio.run(debug_api_transcript_extraction())
