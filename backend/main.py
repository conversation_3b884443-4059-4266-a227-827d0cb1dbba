from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import os
from dotenv import load_dotenv
import logging
from contextlib import asynccontextmanager

# Import API routes
from app.api.routes import video, chat, search
from app.core.config import settings
from app.core.database import engine, Base
from app.services.video_processor import VideoProcessor
# Import services
from app.services.gemini_service import GeminiService
from app.services.native_video_search import NativeVideoSearchService

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting VideoChat AI Backend...")
    
    # Create database tables
    Base.metadata.create_all(bind=engine)
    
    # Create thumbnail directory if it doesn't exist
    os.makedirs("thumbnails", exist_ok=True)
    
    # Create frames directory if it doesn't exist
    os.makedirs("uploads/frames", exist_ok=True)
    
    # Initialize services
    video_processor = VideoProcessor()
    gemini_service = GeminiService()
    native_service = NativeVideoSearchService()

    # Store services in app state
    app.state.video_processor = video_processor
    app.state.gemini_service = gemini_service
    app.state.native_service = native_service
    
    logger.info("Backend startup complete!")
    
    yield
    
    # Shutdown
    logger.info("Shutting down VideoChat AI Backend...")

# Create FastAPI app
app = FastAPI(
    title="VideoChat AI Backend",
    description="Multimodal video analysis system with chat and visual search capabilities",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure with specific origins for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(video.router, prefix="/api/v1/video", tags=["video"])
app.include_router(chat.router, prefix="/api/v1/chat", tags=["chat"])
app.include_router(search.router, prefix="/api/v1/search", tags=["search"])

# Include instant search routes
from app.api.routes import instant_search
app.include_router(instant_search.router, prefix="/api/v1/search", tags=["instant-search"])

# Mount static files for thumbnails and frames
app.mount("/api/thumbnails", StaticFiles(directory="thumbnails"), name="thumbnails")
app.mount("/api/frames", StaticFiles(directory="uploads/frames"), name="frames")

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "VideoChat AI Backend is running!",
        "version": "1.0.0",
        "status": "healthy"
    }

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "services": {
            "database": "connected",
            "gemini": "available",
            "video_processor": "ready",
            "native_search": "enabled" if hasattr(app.state, 'native_service') and app.state.native_service else "disabled"
        },
        "optimizations": {
            "ultra_fast_sampling": True,
            "concurrent_frames": 8,
            "native_video_search": hasattr(app.state, 'native_service') and app.state.native_service is not None
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,  # Changed from 8000 to 8001 to match frontend
        log_level="info"
    )
