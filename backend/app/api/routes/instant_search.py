"""
Instant Search Routes

Provides millisecond-latency search using pre-indexed content
"""

from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict
import logging
import time

from app.core.database import get_db
from app.models.video import Video
from app.services.instant_video_search import InstantVideoSearch
from app.api.routes.search import VisualSearchRequest

logger = logging.getLogger(__name__)
router = APIRouter()

# Global instant search service
instant_search_service = InstantVideoSearch()


@router.post("/instant")
async def instant_visual_search(
    request: Request,
    search_request: VisualSearchRequest,
    db: Session = Depends(get_db)
) -> Dict:
    """
    Instant visual search with millisecond response times
    
    Prerequisites:
    - Video must be pre-indexed using /index endpoint
    - Results are based on pre-computed embeddings
    """
    start_time = time.time()
    
    try:
        # Validate video exists
        video = db.query(Video).filter(Video.id == search_request.video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        # Check if video is indexed
        index_status = await instant_search_service.get_index_status(search_request.video_id)
        if not index_status['ready_for_instant_search']:
            raise HTTPException(
                status_code=400, 
                detail="Video not indexed. Please index the video first using POST /api/v1/search/index/{video_id}"
            )
        
        # Perform instant search
        results = await instant_search_service.instant_search(
            video_id=search_request.video_id,
            query=search_request.query,
            max_results=search_request.max_results
        )
        
        # Add performance metrics
        total_time = time.time() - start_time
        results['response_time_ms'] = int(total_time * 1000)
        results['index_frame_count'] = index_status['frame_count']
        
        logger.info(f"⚡ Instant search for '{search_request.query}' completed in {results['response_time_ms']}ms")
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Instant search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/index/{video_id}")
async def index_video_for_search(
    request: Request,
    video_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
) -> Dict:
    """
    Pre-index video for instant search
    
    This processes the video and creates searchable embeddings.
    Should be called after video upload for instant search capability.
    """
    try:
        # Validate video exists
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.file_path:
            raise HTTPException(status_code=400, detail="Video file not found")
        
        # Check if already indexed
        index_status = await instant_search_service.get_index_status(video_id)
        if index_status['ready_for_instant_search']:
            return {
                "message": "Video already indexed",
                "indexed_frames": index_status['frame_count'],
                "ready_for_instant_search": True
            }
        
        # Start indexing in background
        background_tasks.add_task(
            instant_search_service.index_video,
            video_id=video_id,
            video_path=video.file_path,
            gemini_service=request.app.state.gemini_service
        )
        
        return {
            "message": "Video indexing started",
            "video_id": video_id,
            "status": "indexing",
            "estimated_time": "30-60 seconds"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Indexing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/index/{video_id}/status")
async def get_index_status(
    video_id: int,
    db: Session = Depends(get_db)
) -> Dict:
    """Check if video is indexed and ready for instant search"""
    
    # Validate video exists
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Get index status
    status = await instant_search_service.get_index_status(video_id)
    
    return {
        "video_id": video_id,
        "indexed": status['indexed'],
        "frame_count": status['frame_count'],
        "ready_for_instant_search": status['ready_for_instant_search'],
        "embedding_status": video.embedding_status
    }


@router.post("/local/objects")
async def local_object_search(
    request: Request,
    search_request: VisualSearchRequest,
    db: Session = Depends(get_db)
) -> Dict:
    """
    Fast local object detection using YOLO
    
    No API calls - runs entirely locally
    Response time: 2-5 seconds for most videos
    """
    start_time = time.time()
    
    try:
        # Validate video
        video = db.query(Video).filter(Video.id == search_request.video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.file_path:
            raise HTTPException(status_code=400, detail="Video file not found")
        
        # Use local object detector
        from app.services.instant_video_search import LocalObjectDetector
        detector = LocalObjectDetector()
        
        if not detector.available:
            raise HTTPException(
                status_code=503,
                detail="YOLO not installed. Run: pip install ultralytics"
            )
        
        # Parse query for objects
        objects = [obj.strip() for obj in search_request.query.lower().split(',')]
        
        # Search
        results = await detector.search_objects(video.file_path, objects)
        
        # Format response
        search_time = time.time() - start_time
        
        return {
            'query': search_request.query,
            'results': results[:search_request.max_results],
            'total_results': len(results),
            'processing_time': search_time,
            'processing_method': 'local_yolo_detection',
            'response_time_ms': int(search_time * 1000)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Local search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/local/clip")
async def local_clip_search(
    request: Request,
    search_request: VisualSearchRequest,
    db: Session = Depends(get_db)
) -> Dict:
    """
    Fast local search using CLIP embeddings
    
    Uses OpenAI's CLIP model for image-text matching
    Response time: 3-8 seconds for most videos
    """
    start_time = time.time()
    
    try:
        # Validate video
        video = db.query(Video).filter(Video.id == search_request.video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.file_path:
            raise HTTPException(status_code=400, detail="Video file not found")
        
        # Use CLIP search
        from app.services.instant_video_search import CLIPVideoSearch
        clip_search = CLIPVideoSearch()
        
        if not clip_search.available:
            raise HTTPException(
                status_code=503,
                detail="CLIP not installed. Run: pip install git+https://github.com/openai/CLIP.git"
            )
        
        # Search
        results = await clip_search.search(video.file_path, search_request.query)
        
        # Format response
        search_time = time.time() - start_time
        
        # Convert to standard format
        formatted_results = []
        for r in results[:search_request.max_results]:
            formatted_results.append({
                'timestamp': r['timestamp'],
                'confidence': r['confidence'],
                'description': r['description'],
                'frame_path': f"/api/v1/search/{search_request.video_id}/frame?timestamp={r['timestamp']}",
                'clip_start': max(0, r['timestamp'] - 5),
                'clip_end': r['timestamp'] + 10
            })
        
        return {
            'query': search_request.query,
            'results': formatted_results,
            'total_results': len(results),
            'processing_time': search_time,
            'processing_method': 'local_clip_search',
            'response_time_ms': int(search_time * 1000)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"CLIP search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))