"""
Optimized Video Search Service

Combines the best of frame-based and native video search with performance optimizations.
"""

import asyncio
import cv2
import hashlib
import json
import logging
import os
import time
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Optional, Tuple
import numpy as np
from dataclasses import dataclass
import redis

from app.core.config import settings
from app.services.native_video_search import VideoClip

logger = logging.getLogger(__name__)


@dataclass
class OptimizationConfig:
    """Configuration for search optimizations"""
    # Frame extraction
    use_scene_detection: bool = True
    min_scene_duration: float = 1.0  # seconds
    scene_threshold: float = 30.0  # difference threshold
    
    # Frame processing
    resize_width: int = 320  # Resize frames for faster processing
    jpeg_quality: int = 30  # Lower quality for faster encoding
    
    # Caching
    enable_cache: bool = True
    cache_ttl: int = 3600  # 1 hour
    
    # Concurrency
    max_concurrent_frames: int = 8  # Process more frames in parallel
    batch_size: int = 5  # Batch frames to Gemini API
    
    # Search strategy
    use_hybrid_search: bool = True  # Combine frame and native search
    native_search_threshold: int = 300  # Use native for videos < 5 minutes


class OptimizedVideoSearch:
    """
    High-performance video search with multiple optimization strategies
    """
    
    def __init__(self, gemini_service, native_service=None):
        self.gemini_service = gemini_service
        self.native_service = native_service
        self.config = OptimizationConfig()
        
        # Thread pool for concurrent processing
        self.executor = ThreadPoolExecutor(max_workers=self.config.max_concurrent_frames)
        
        # Redis cache
        try:
            self.redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)
            self.redis_client.ping()
            self.cache_enabled = self.config.enable_cache
            logger.info("✅ Redis cache connected")
        except:
            self.cache_enabled = False
            self.redis_client = None
            logger.warning("Redis cache unavailable")
    
    async def search(self, video_path: str, query: str, video_id: int) -> Dict:
        """
        Optimized search using the best strategy for the video
        """
        start_time = time.time()
        
        # Check cache first
        if self.cache_enabled:
            cache_key = self._generate_cache_key(video_path, query)
            cached_result = await self._get_cached_results(cache_key)
            if cached_result:
                logger.info(f"⚡ Cache hit for '{query}' - 0ms response time")
                return cached_result
        
        # Get video info
        video_info = self._get_video_info(video_path)
        duration = video_info['duration']
        
        # Choose search strategy based on video length and configuration
        if self.config.use_hybrid_search and duration < self.config.native_search_threshold:
            # Use native search for short videos (< 5 minutes)
            logger.info(f"🎯 Using native video search for {duration:.1f}s video")
            results = await self._native_search(video_path, query, video_id)
        else:
            # Use optimized frame search for longer videos
            logger.info(f"🔍 Using optimized frame search for {duration:.1f}s video")
            results = await self._optimized_frame_search(video_path, query, video_id, video_info)
        
        # Cache results
        if self.cache_enabled and results['results']:
            await self._cache_results(cache_key, results)
        
        processing_time = time.time() - start_time
        results['processing_time'] = processing_time
        logger.info(f"✅ Search completed in {processing_time:.2f}s")
        
        return results
    
    async def _native_search(self, video_path: str, query: str, video_id: int) -> Dict:
        """Use Gemini 2.5's native video understanding"""
        if not self.native_service:
            return self._empty_response(query)
        
        try:
            # Use native video search
            clips = await self.native_service.search_visual_content(video_path, query)
            
            # Convert to search results
            results = []
            for clip in clips:
                results.append({
                    'timestamp': clip.start_time,
                    'confidence': clip.confidence * 100,
                    'description': clip.description,
                    'frame_path': f"/api/v1/search/{video_id}/frame?timestamp={clip.start_time}",
                    'clip_start': clip.start_time,
                    'clip_end': clip.end_time
                })
            
            return {
                'query': query,
                'results': results,
                'clips': [clip.to_dict() for clip in clips],
                'total_results': len(results),
                'processing_method': 'native_video_understanding'
            }
            
        except Exception as e:
            logger.error(f"Native search failed: {e}")
            return self._empty_response(query)
    
    async def _optimized_frame_search(self, video_path: str, query: str, video_id: int, video_info: Dict) -> Dict:
        """Optimized frame-based search with multiple performance improvements"""
        
        # Extract frames intelligently
        if self.config.use_scene_detection:
            frames = await self._extract_frames_with_scene_detection(video_path, video_info)
            logger.info(f"📸 Extracted {len(frames)} key frames using scene detection")
        else:
            frames = await self._extract_frames_uniform(video_path, video_info)
            logger.info(f"📸 Extracted {len(frames)} frames uniformly")
        
        # Process frames in batches
        results = []
        
        if self.config.batch_size > 1:
            # Batch processing for better API efficiency
            for i in range(0, len(frames), self.config.batch_size):
                batch = frames[i:i + self.config.batch_size]
                batch_results = await self._process_frame_batch(batch, query, video_id)
                results.extend(batch_results)
        else:
            # Concurrent individual processing
            tasks = [self._process_frame(frame, query, video_id) for frame in frames]
            frame_results = await asyncio.gather(*tasks, return_exceptions=True)
            results = [r for r in frame_results if r and not isinstance(r, Exception)]
        
        # Sort by confidence
        results.sort(key=lambda x: x['confidence'], reverse=True)
        
        # Convert to clips
        clips = self._results_to_clips(results)
        
        return {
            'query': query,
            'results': results,
            'clips': clips,
            'total_results': len(results),
            'processing_method': 'optimized_frame_analysis'
        }
    
    async def _extract_frames_with_scene_detection(self, video_path: str, video_info: Dict) -> List[Tuple[float, np.ndarray]]:
        """Extract frames at scene changes for more relevant sampling"""
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return []
        
        frames = []
        fps = video_info['fps']
        duration = video_info['duration']
        
        # Initialize
        prev_frame = None
        prev_hist = None
        scene_start = 0
        frame_count = 0
        
        # Process video
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            timestamp = frame_count / fps
            frame_count += 1
            
            # Skip if too close to last scene
            if timestamp - scene_start < self.config.min_scene_duration:
                continue
            
            # Resize for faster processing
            small_frame = cv2.resize(frame, (160, 120))
            
            # Calculate histogram
            hist = cv2.calcHist([small_frame], [0, 1, 2], None, [8, 8, 8], [0, 256, 0, 256, 0, 256])
            hist = cv2.normalize(hist, hist).flatten()
            
            # Detect scene change
            if prev_hist is not None:
                diff = cv2.compareHist(prev_hist, hist, cv2.HISTCMP_CHISQR)
                
                if diff > self.config.scene_threshold:
                    # Scene change detected
                    frames.append((timestamp, frame))
                    scene_start = timestamp
                    logger.debug(f"Scene change at {timestamp:.1f}s (diff: {diff:.1f})")
            
            prev_hist = hist
            
            # Limit total frames
            if len(frames) >= settings.MAX_FRAMES_PER_VIDEO:
                break
        
        cap.release()
        
        # Ensure minimum coverage
        if len(frames) < 10:
            # Fall back to uniform sampling
            return await self._extract_frames_uniform(video_path, video_info)
        
        return frames
    
    async def _extract_frames_uniform(self, video_path: str, video_info: Dict) -> List[Tuple[float, np.ndarray]]:
        """Extract frames at uniform intervals"""
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return []
        
        frames = []
        duration = video_info['duration']
        fps = video_info['fps']
        
        # Calculate interval
        if duration <= 60:  # 1 minute
            interval = 5  # Every 5 seconds
        elif duration <= 300:  # 5 minutes
            interval = 10  # Every 10 seconds
        else:
            interval = duration / 30  # Max 30 frames
        
        # Extract frames
        for timestamp in range(0, int(duration), int(interval)):
            frame_number = int(timestamp * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            ret, frame = cap.read()
            if ret:
                frames.append((float(timestamp), frame))
        
        cap.release()
        return frames
    
    async def _process_frame_batch(self, frames: List[Tuple[float, np.ndarray]], query: str, video_id: int) -> List[Dict]:
        """Process multiple frames in a single API call"""
        results = []
        
        try:
            # Prepare frames
            frame_data = []
            for timestamp, frame in frames:
                # Resize frame
                if frame.shape[1] > self.config.resize_width:
                    scale = self.config.resize_width / frame.shape[1]
                    new_height = int(frame.shape[0] * scale)
                    frame = cv2.resize(frame, (self.config.resize_width, new_height))
                
                # Encode to JPEG
                success, encoded = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, self.config.jpeg_quality])
                if success:
                    frame_data.append({
                        'timestamp': timestamp,
                        'data': encoded.tobytes()
                    })
            
            # Create batch prompt
            batch_prompt = f"""
            Analyze these {len(frame_data)} video frames and find any instances of "{query}".
            
            For each frame that contains "{query}", respond with:
            - Frame number (1-{len(frame_data)})
            - Confidence (0.0-1.0)
            - Description of what you see
            
            If no frames contain "{query}", say "No matches found".
            """
            
            # Send to Gemini (would need to implement batch processing in gemini_service)
            # For now, process individually
            for i, (timestamp, frame) in enumerate(frames):
                result = await self._process_frame((timestamp, frame), query, video_id)
                if result:
                    results.append(result)
            
        except Exception as e:
            logger.error(f"Batch processing error: {e}")
        
        return results
    
    async def _process_frame(self, frame_data: Tuple[float, np.ndarray], query: str, video_id: int) -> Optional[Dict]:
        """Process a single frame"""
        timestamp, frame = frame_data
        
        try:
            # Resize frame for faster processing
            if frame.shape[1] > self.config.resize_width:
                scale = self.config.resize_width / frame.shape[1]
                new_height = int(frame.shape[0] * scale)
                frame = cv2.resize(frame, (self.config.resize_width, new_height))
            
            # Encode frame
            success, encoded = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, self.config.jpeg_quality])
            if not success:
                return None
            
            # Save temporarily
            import tempfile
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=True) as tmp:
                tmp.write(encoded.tobytes())
                tmp.flush()
                
                # Analyze with Gemini
                analysis = await self.gemini_service.analyze_frame_for_search(tmp.name, query)
                
                if analysis.get('match', False):
                    return {
                        'timestamp': timestamp,
                        'confidence': analysis.get('confidence', 0.5) * 100,
                        'description': analysis.get('description', f"Found '{query}' at {timestamp:.1f}s"),
                        'frame_path': f"/api/v1/search/{video_id}/frame?timestamp={timestamp}",
                        'clip_start': max(0, timestamp - 5),
                        'clip_end': timestamp + 10
                    }
        
        except Exception as e:
            logger.warning(f"Frame processing error at {timestamp}s: {e}")
        
        return None
    
    def _results_to_clips(self, results: List[Dict]) -> List[Dict]:
        """Convert frame results to video clips"""
        if not results:
            return []
        
        clips = []
        current_clip = None
        
        for result in sorted(results, key=lambda x: x['timestamp']):
            if current_clip is None:
                current_clip = {
                    'start_time': result['clip_start'],
                    'end_time': result['clip_end'],
                    'confidence': result['confidence'],
                    'description': result['description'],
                    'frame_count': 1,
                    'thumbnail_url': result['frame_path']
                }
            elif result['timestamp'] - current_clip['end_time'] < 5:
                # Merge with current clip
                current_clip['end_time'] = result['clip_end']
                current_clip['confidence'] = max(current_clip['confidence'], result['confidence'])
                current_clip['frame_count'] += 1
            else:
                # Start new clip
                clips.append(current_clip)
                current_clip = {
                    'start_time': result['clip_start'],
                    'end_time': result['clip_end'],
                    'confidence': result['confidence'],
                    'description': result['description'],
                    'frame_count': 1,
                    'thumbnail_url': result['frame_path']
                }
        
        if current_clip:
            clips.append(current_clip)
        
        return clips
    
    def _get_video_info(self, video_path: str) -> Dict:
        """Get video information"""
        cap = cv2.VideoCapture(video_path)
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        return {
            'fps': fps,
            'frame_count': frame_count,
            'width': width,
            'height': height,
            'duration': duration
        }
    
    def _generate_cache_key(self, video_path: str, query: str) -> str:
        """Generate cache key"""
        video_hash = hashlib.md5(video_path.encode()).hexdigest()[:8]
        query_hash = hashlib.md5(query.lower().strip().encode()).hexdigest()[:8]
        return f"search:v2:{video_hash}:{query_hash}"
    
    async def _get_cached_results(self, cache_key: str) -> Optional[Dict]:
        """Get cached results"""
        if not self.cache_enabled:
            return None
        
        try:
            cached = self.redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
        except Exception as e:
            logger.warning(f"Cache read error: {e}")
        
        return None
    
    async def _cache_results(self, cache_key: str, results: Dict):
        """Cache results"""
        if not self.cache_enabled:
            return
        
        try:
            self.redis_client.setex(
                cache_key,
                self.config.cache_ttl,
                json.dumps(results)
            )
        except Exception as e:
            logger.warning(f"Cache write error: {e}")
    
    def _empty_response(self, query: str) -> Dict:
        """Return empty response"""
        return {
            'query': query,
            'results': [],
            'clips': [],
            'total_results': 0,
            'processing_method': 'none'
        }


# Additional optimization: Pre-process videos on upload
async def preprocess_video_for_search(video_path: str, video_id: int):
    """
    Pre-process video after upload to speed up searches
    
    This can:
    1. Extract and cache key frames
    2. Generate scene detection data
    3. Upload to Gemini for native search
    4. Create frame embeddings
    """
    logger.info(f"Pre-processing video {video_id} for optimized search")
    
    try:
        # Extract key frames using scene detection
        optimizer = OptimizedVideoSearch(None)
        video_info = optimizer._get_video_info(video_path)
        
        # Extract frames
        frames = await optimizer._extract_frames_with_scene_detection(video_path, video_info)
        
        # Save frame metadata
        frame_metadata = {
            'video_id': video_id,
            'frame_count': len(frames),
            'timestamps': [f[0] for f in frames],
            'duration': video_info['duration'],
            'preprocessing_date': time.time()
        }
        
        # Cache metadata
        if optimizer.cache_enabled:
            cache_key = f"video:metadata:{video_id}"
            optimizer.redis_client.setex(
                cache_key,
                86400,  # 24 hours
                json.dumps(frame_metadata)
            )
        
        logger.info(f"✅ Pre-processed video {video_id}: {len(frames)} key frames extracted")
        
    except Exception as e:
        logger.error(f"Pre-processing failed for video {video_id}: {e}")