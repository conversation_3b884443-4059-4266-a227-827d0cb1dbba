"""
Instant Video Search Service

Provides near-instant search by pre-indexing video content and using
efficient search methods including embeddings, local models, and caching.
"""

import asyncio
import cv2
import numpy as np
import logging
import time
import json
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import torch
from sentence_transformers import SentenceTransformer
import chromadb
from chromadb.utils import embedding_functions
from PIL import Image
import io

from app.core.config import settings
from app.core.database import get_db
from app.models.video import Video, VideoFrame

logger = logging.getLogger(__name__)


@dataclass
class SearchResult:
    timestamp: float
    confidence: float
    description: str
    frame_id: int
    similarity_score: float


class InstantVideoSearch:
    """
    Provides instant video search using pre-computed embeddings and vector search
    """
    
    def __init__(self):
        # Initialize embedding models
        self.text_model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
        
        # Initialize ChromaDB for vector search
        self.chroma_client = chromadb.PersistentClient(path=settings.CHROMA_PERSIST_DIRECTORY)
        
        # Create or get collections
        self.frame_collection = self.chroma_client.get_or_create_collection(
            name="video_frames",
            embedding_function=embedding_functions.SentenceTransformerEmbeddingFunction(
                model_name="sentence-transformers/all-MiniLM-L6-v2"
            )
        )
        
        # Try to load CLIP for image embeddings (optional but recommended)
        try:
            import clip
            self.clip_model, self.clip_preprocess = clip.load("ViT-B/32", device="cpu")
            self.clip_available = True
            logger.info("✅ CLIP model loaded for image embeddings")
        except:
            self.clip_available = False
            logger.warning("CLIP not available - using text-only embeddings")
        
        # Try to load YOLO for object detection (optional)
        try:
            from ultralytics import YOLO
            self.yolo_model = YOLO('yolov8n.pt')  # Nano model for speed
            self.yolo_available = True
            logger.info("✅ YOLO model loaded for object detection")
        except:
            self.yolo_available = False
            logger.warning("YOLO not available - using embeddings only")
    
    async def index_video(self, video_id: int, video_path: str, gemini_service=None):
        """
        Pre-process and index video for instant search
        This should be called after video upload
        """
        start_time = time.time()
        logger.info(f"🎬 Starting video indexing for video {video_id}")
        
        try:
            # Extract key frames
            frames = await self._extract_key_frames(video_path)
            logger.info(f"📸 Extracted {len(frames)} key frames")
            
            # Process frames in batches
            batch_size = 5
            indexed_count = 0
            
            for i in range(0, len(frames), batch_size):
                batch = frames[i:i + batch_size]
                
                # Process batch
                if gemini_service and i == 0:  # Use Gemini for first batch only (to save API calls)
                    await self._index_batch_with_gemini(video_id, batch, gemini_service)
                else:
                    await self._index_batch_locally(video_id, batch)
                
                indexed_count += len(batch)
                logger.info(f"📊 Indexed {indexed_count}/{len(frames)} frames")
            
            # Update video status
            from app.core.database import SessionLocal
            db = SessionLocal()
            video = db.query(Video).filter(Video.id == video_id).first()
            if video:
                video.embedding_status = "completed"
                db.commit()
            db.close()
            
            indexing_time = time.time() - start_time
            logger.info(f"✅ Video indexing completed in {indexing_time:.2f}s")
            
            return {
                "status": "success",
                "frames_indexed": len(frames),
                "indexing_time": indexing_time
            }
            
        except Exception as e:
            logger.error(f"❌ Video indexing failed: {e}")
            return {
                "status": "failed",
                "error": str(e)
            }
    
    async def instant_search(self, video_id: int, query: str, max_results: int = 10) -> Dict:
        """
        Perform instant search using pre-computed embeddings
        Returns results in milliseconds
        """
        start_time = time.time()
        
        try:
            # Generate query embedding
            query_embedding = self.text_model.encode(query).tolist()
            
            # Search in vector database
            results = self.frame_collection.query(
                query_embeddings=[query_embedding],
                n_results=max_results,
                where={"video_id": video_id}
            )
            
            if not results['ids'][0]:
                return self._empty_response(query)
            
            # Parse results
            search_results = []
            for i, doc_id in enumerate(results['ids'][0]):
                metadata = results['metadatas'][0][i]
                distance = results['distances'][0][i]
                
                # Convert distance to confidence (0-100)
                confidence = max(0, min(100, (2 - distance) * 50))
                
                search_results.append({
                    'timestamp': metadata['timestamp'],
                    'confidence': confidence,
                    'description': metadata.get('description', f"Match for '{query}'"),
                    'frame_path': f"/api/v1/search/{video_id}/frame?timestamp={metadata['timestamp']}",
                    'objects': metadata.get('objects', []),
                    'similarity_score': 1 - (distance / 2)  # Normalize to 0-1
                })
            
            # Sort by confidence
            search_results.sort(key=lambda x: x['confidence'], reverse=True)
            
            # Group into clips
            clips = self._group_into_clips(search_results)
            
            search_time = time.time() - start_time
            
            return {
                'query': query,
                'results': search_results,
                'clips': clips,
                'total_results': len(search_results),
                'processing_time': search_time,
                'processing_method': 'instant_embedding_search',
                'search_time_ms': int(search_time * 1000)
            }
            
        except Exception as e:
            logger.error(f"Instant search failed: {e}")
            return self._empty_response(query)
    
    async def _extract_key_frames(self, video_path: str) -> List[Tuple[float, np.ndarray]]:
        """Extract key frames using scene detection"""
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError("Cannot open video file")
        
        frames = []
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0
        
        # Adaptive sampling based on video length
        if duration <= 60:  # 1 minute
            interval = 3  # Every 3 seconds
        elif duration <= 300:  # 5 minutes
            interval = 5  # Every 5 seconds
        elif duration <= 1800:  # 30 minutes
            interval = 10  # Every 10 seconds
        else:
            interval = 30  # Every 30 seconds
        
        # Extract frames
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            timestamp = frame_count / fps
            
            # Sample at interval
            if frame_count % (interval * fps) == 0:
                frames.append((timestamp, frame))
            
            frame_count += 1
            
            # Limit frames
            if len(frames) >= 100:  # Max 100 frames per video
                break
        
        cap.release()
        return frames
    
    async def _index_batch_locally(self, video_id: int, frames: List[Tuple[float, np.ndarray]]):
        """Index frames using local models"""
        
        documents = []
        embeddings = []
        metadatas = []
        ids = []
        
        for timestamp, frame in frames:
            # Generate description and objects
            description = f"Frame at {timestamp:.1f}s"
            objects = []
            
            # Use YOLO for object detection if available
            if self.yolo_available:
                try:
                    results = self.yolo_model(frame, verbose=False)
                    for r in results:
                        if r.boxes is not None:
                            for box in r.boxes:
                                cls = int(box.cls)
                                conf = float(box.conf)
                                if conf > 0.5:
                                    label = self.yolo_model.names[cls]
                                    objects.append(label)
                    
                    if objects:
                        description = f"Frame at {timestamp:.1f}s showing: {', '.join(set(objects))}"
                except Exception as e:
                    logger.warning(f"YOLO detection failed: {e}")
            
            # Generate text for embedding
            text = f"{description} {' '.join(objects)}"
            
            # Store in ChromaDB
            doc_id = f"v{video_id}_t{int(timestamp*10)}"
            
            documents.append(text)
            metadatas.append({
                "video_id": video_id,
                "timestamp": timestamp,
                "description": description,
                "objects": list(set(objects))
            })
            ids.append(doc_id)
        
        # Add to ChromaDB
        if documents:
            self.frame_collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
    
    async def _index_batch_with_gemini(self, video_id: int, frames: List[Tuple[float, np.ndarray]], gemini_service):
        """Index frames using Gemini for rich descriptions"""
        
        documents = []
        metadatas = []
        ids = []
        
        for timestamp, frame in frames:
            try:
                # Save frame temporarily
                import tempfile
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=True) as tmp:
                    cv2.imwrite(tmp.name, frame)
                    
                    # Get comprehensive analysis from Gemini
                    prompt = """Analyze this video frame and provide:
                    1. A detailed description of what's visible
                    2. List of all objects, people, text, or notable elements
                    3. The scene type (indoor/outdoor, setting)
                    4. Any actions or activities happening
                    
                    Be specific and comprehensive for search purposes."""
                    
                    analysis = await gemini_service.analyze_image(tmp.name, prompt)
                    
                    # Parse response
                    description = analysis.get('description', f"Frame at {timestamp:.1f}s")
                    objects = analysis.get('objects', [])
                    scene = analysis.get('scene', '')
                    
                    # Combine for rich embedding
                    text = f"{description} {' '.join(objects)} {scene}"
                    
            except Exception as e:
                logger.warning(f"Gemini analysis failed for frame at {timestamp}: {e}")
                text = f"Frame at {timestamp:.1f}s"
                description = text
                objects = []
            
            # Store in ChromaDB
            doc_id = f"v{video_id}_t{int(timestamp*10)}"
            
            documents.append(text)
            metadatas.append({
                "video_id": video_id,
                "timestamp": timestamp,
                "description": description,
                "objects": objects
            })
            ids.append(doc_id)
        
        # Add to ChromaDB
        if documents:
            self.frame_collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
    
    def _group_into_clips(self, results: List[Dict]) -> List[Dict]:
        """Group search results into video clips"""
        if not results:
            return []
        
        clips = []
        current_clip = None
        
        for result in sorted(results, key=lambda x: x['timestamp']):
            if current_clip is None:
                current_clip = {
                    'start_time': max(0, result['timestamp'] - 5),
                    'end_time': result['timestamp'] + 10,
                    'confidence': result['confidence'],
                    'description': result['description'],
                    'frame_count': 1,
                    'thumbnail_url': result['frame_path']
                }
            elif result['timestamp'] - current_clip['end_time'] < 5:
                # Extend current clip
                current_clip['end_time'] = result['timestamp'] + 10
                current_clip['confidence'] = max(current_clip['confidence'], result['confidence'])
                current_clip['frame_count'] += 1
            else:
                # Start new clip
                clips.append(current_clip)
                current_clip = {
                    'start_time': max(0, result['timestamp'] - 5),
                    'end_time': result['timestamp'] + 10,
                    'confidence': result['confidence'],
                    'description': result['description'],
                    'frame_count': 1,
                    'thumbnail_url': result['frame_path']
                }
        
        if current_clip:
            clips.append(current_clip)
        
        return clips
    
    def _empty_response(self, query: str) -> Dict:
        """Return empty search response"""
        return {
            'query': query,
            'results': [],
            'clips': [],
            'total_results': 0,
            'processing_time': 0,
            'processing_method': 'instant_embedding_search'
        }
    
    async def get_index_status(self, video_id: int) -> Dict:
        """Check if video is indexed and ready for instant search"""
        try:
            # Query for any frames from this video
            results = self.frame_collection.query(
                query_embeddings=[[0] * 384],  # Dummy embedding
                n_results=1,
                where={"video_id": video_id}
            )
            
            frame_count = len(results['ids'][0]) if results['ids'][0] else 0
            
            return {
                "indexed": frame_count > 0,
                "frame_count": frame_count,
                "ready_for_instant_search": frame_count > 0
            }
            
        except Exception as e:
            logger.error(f"Error checking index status: {e}")
            return {
                "indexed": False,
                "frame_count": 0,
                "ready_for_instant_search": False
            }


# Additional fast search strategies

class LocalObjectDetector:
    """Fast local object detection using YOLO"""
    
    def __init__(self):
        try:
            from ultralytics import YOLO
            self.model = YOLO('yolov8n.pt')  # Nano for speed
            self.available = True
        except:
            self.available = False
    
    async def search_objects(self, video_path: str, target_objects: List[str]) -> List[Dict]:
        """Search for specific objects in video"""
        if not self.available:
            return []
        
        results = []
        cap = cv2.VideoCapture(video_path)
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Process every 30th frame (1 second at 30fps)
            if frame_count % 30 == 0:
                timestamp = frame_count / fps
                
                # Run detection
                detections = self.model(frame, verbose=False)
                
                for r in detections:
                    if r.boxes is not None:
                        for box in r.boxes:
                            cls = int(box.cls)
                            conf = float(box.conf)
                            label = self.model.names[cls].lower()
                            
                            # Check if detected object matches target
                            for target in target_objects:
                                if target.lower() in label or label in target.lower():
                                    results.append({
                                        'timestamp': timestamp,
                                        'object': label,
                                        'confidence': conf * 100,
                                        'match': target
                                    })
            
            frame_count += 1
        
        cap.release()
        return results


class CLIPVideoSearch:
    """Fast video search using CLIP embeddings"""
    
    def __init__(self):
        try:
            import clip
            self.model, self.preprocess = clip.load("ViT-B/32", device="cpu")
            self.available = True
        except:
            self.available = False
    
    async def search(self, video_path: str, text_query: str) -> List[Dict]:
        """Search video using CLIP image-text matching"""
        if not self.available:
            return []
        
        results = []
        cap = cv2.VideoCapture(video_path)
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        # Encode text query
        text = clip.tokenize([text_query])
        with torch.no_grad():
            text_features = self.model.encode_text(text)
        
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Process every 60th frame (2 seconds at 30fps)
            if frame_count % 60 == 0:
                timestamp = frame_count / fps
                
                # Convert to PIL Image
                image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                image = self.preprocess(image).unsqueeze(0)
                
                # Get image features
                with torch.no_grad():
                    image_features = self.model.encode_image(image)
                
                # Calculate similarity
                similarity = (100.0 * image_features @ text_features.T).softmax(dim=-1)
                score = similarity[0][0].item()
                
                if score > 0.25:  # Threshold
                    results.append({
                        'timestamp': timestamp,
                        'confidence': score * 100,
                        'description': f"Match for '{text_query}'"
                    })
            
            frame_count += 1
        
        cap.release()
        return results