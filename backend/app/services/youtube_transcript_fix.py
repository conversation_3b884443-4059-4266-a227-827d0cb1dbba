"""
YouTube Transcript Extraction Fix

This module provides a robust solution for extracting YouTube transcripts,
handling various edge cases and API issues.
"""

import re
import json
import logging
import subprocess
from typing import Dict, List, Optional, Tuple
import httpx
from youtube_transcript_api import YouTubeTranscript<PERSON>pi, TranscriptList
from youtube_transcript_api._errors import TranscriptsDisabled, NoTranscriptFound
import xml.etree.ElementTree as ET

logger = logging.getLogger(__name__)


class YouTubeTranscriptExtractor:
    """Enhanced YouTube transcript extractor with multiple fallback methods"""
    
    def __init__(self):
        self.session = httpx.Client(timeout=30)
    
    def extract_transcript(self, video_id: str, preferred_languages: List[str] = None) -> Dict:
        """
        Extract transcript using multiple methods with fallbacks
        
        Returns:
            Dict with keys: transcript, text, language, type
        """
        if preferred_languages is None:
            preferred_languages = ['en', 'en-US', 'en-GB']
        
        # Method 1: Try the standard youtube-transcript-api
        result = self._try_standard_api(video_id, preferred_languages)
        if result:
            return result
        
        # Method 2: Try direct API call with custom parsing
        result = self._try_direct_api(video_id)
        if result:
            return result
        
        # Method 3: Try yt-dlp with subtitle download
        result = self._try_ytdlp_method(video_id)
        if result:
            return result
        
        # Method 4: Try web scraping as last resort
        result = self._try_web_scraping(video_id)
        if result:
            return result
        
        raise Exception("No transcript available after trying all methods")
    
    def _try_standard_api(self, video_id: str, languages: List[str]) -> Optional[Dict]:
        """Try the standard youtube-transcript-api approach"""
        try:
            transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
            
            # Try each language in order of preference
            for lang in languages:
                try:
                    # Try manual transcripts first
                    transcript = transcript_list.find_manually_created_transcript([lang])
                    data = self._safe_fetch_transcript(transcript)
                    if data:
                        return {
                            "transcript": data,
                            "text": self._format_transcript(data),
                            "language": transcript.language,
                            "type": "manual"
                        }
                except:
                    pass
                
                try:
                    # Try auto-generated transcripts
                    transcript = transcript_list.find_generated_transcript([lang])
                    data = self._safe_fetch_transcript(transcript)
                    if data:
                        return {
                            "transcript": data,
                            "text": self._format_transcript(data),
                            "language": transcript.language,
                            "type": "auto"
                        }
                except:
                    pass
            
            # Try any available transcript
            for transcript in transcript_list:
                data = self._safe_fetch_transcript(transcript)
                if data:
                    return {
                        "transcript": data,
                        "text": self._format_transcript(data),
                        "language": transcript.language,
                        "type": "any"
                    }
                    
        except Exception as e:
            logger.warning(f"Standard API failed: {e}")
        
        return None
    
    def _safe_fetch_transcript(self, transcript) -> Optional[List[Dict]]:
        """Safely fetch transcript data with error handling"""
        try:
            # Get the transcript URL
            url = transcript._url
            
            # Make direct HTTP request
            response = self.session.get(url)
            
            if response.status_code != 200:
                return None
            
            # Parse the response
            try:
                # Try parsing as XML
                root = ET.fromstring(response.text)
                
                transcript_data = []
                for text_elem in root.findall('.//text'):
                    start = float(text_elem.get('start', 0))
                    dur = float(text_elem.get('dur', 0))
                    text = text_elem.text or ''
                    
                    transcript_data.append({
                        'text': text,
                        'start': start,
                        'duration': dur
                    })
                
                return transcript_data if transcript_data else None
                
            except ET.ParseError:
                # Try parsing as JSON (newer format)
                try:
                    data = response.json()
                    if 'events' in data:
                        return self._parse_json_transcript(data)
                except:
                    pass
            
            return None
            
        except Exception as e:
            logger.warning(f"Failed to fetch transcript: {e}")
            return None
    
    def _parse_json_transcript(self, data: Dict) -> List[Dict]:
        """Parse JSON format transcript data"""
        transcript_data = []
        
        for event in data.get('events', []):
            if 'segs' in event:
                start_time = event.get('tStartMs', 0) / 1000.0
                
                text_parts = []
                for seg in event['segs']:
                    if 'utf8' in seg:
                        text_parts.append(seg['utf8'])
                
                if text_parts:
                    transcript_data.append({
                        'text': ''.join(text_parts),
                        'start': start_time,
                        'duration': event.get('dDurationMs', 0) / 1000.0
                    })
        
        return transcript_data
    
    def _try_direct_api(self, video_id: str) -> Optional[Dict]:
        """Try direct YouTube API call"""
        try:
            # Get video page to extract necessary data
            url = f"https://www.youtube.com/watch?v={video_id}"
            response = self.session.get(url)
            
            if response.status_code != 200:
                return None
            
            # Extract caption tracks from page
            match = re.search(r'"captions":\s*({[^}]+})', response.text)
            if match:
                caption_data = json.loads(match.group(1))
                
                # Look for caption track URLs
                if 'playerCaptionsTracklistRenderer' in caption_data:
                    tracks = caption_data['playerCaptionsTracklistRenderer'].get('captionTracks', [])
                    
                    for track in tracks:
                        if track.get('languageCode', '').startswith('en'):
                            base_url = track.get('baseUrl')
                            if base_url:
                                # Fetch the captions
                                caption_response = self.session.get(base_url)
                                if caption_response.status_code == 200:
                                    # Parse the caption data
                                    transcript_data = self._parse_caption_response(caption_response.text)
                                    if transcript_data:
                                        return {
                                            "transcript": transcript_data,
                                            "text": self._format_transcript(transcript_data),
                                            "language": track.get('name', {}).get('simpleText', 'English'),
                                            "type": "auto" if track.get('kind') == 'asr' else "manual"
                                        }
        except Exception as e:
            logger.warning(f"Direct API method failed: {e}")
        
        return None
    
    def _parse_caption_response(self, response_text: str) -> List[Dict]:
        """Parse caption response (XML or JSON format)"""
        try:
            # Try XML parsing first
            root = ET.fromstring(response_text)
            transcript_data = []
            
            for text_elem in root.findall('.//text'):
                start = float(text_elem.get('start', 0))
                dur = float(text_elem.get('dur', 0))
                text = text_elem.text or ''
                
                # Clean up the text
                text = text.replace('\\n', ' ').strip()
                
                if text:
                    transcript_data.append({
                        'text': text,
                        'start': start,
                        'duration': dur
                    })
            
            return transcript_data
            
        except:
            # Try JSON parsing
            try:
                data = json.loads(response_text)
                return self._parse_json_transcript(data)
            except:
                return []
    
    def _try_ytdlp_method(self, video_id: str) -> Optional[Dict]:
        """Use yt-dlp to download and parse subtitles"""
        try:
            # Create a temporary file for subtitles
            import tempfile
            import os
            
            with tempfile.TemporaryDirectory() as temp_dir:
                output_path = os.path.join(temp_dir, "%(title)s.%(ext)s")
                
                # Download subtitles only
                cmd = [
                    'yt-dlp',
                    '--skip-download',
                    '--write-auto-sub',
                    '--sub-format', 'vtt/srt/json3',
                    '--sub-langs', 'en.*,en',
                    '--output', output_path,
                    f'https://www.youtube.com/watch?v={video_id}'
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=temp_dir)
                
                if result.returncode == 0:
                    # Look for downloaded subtitle files
                    for filename in os.listdir(temp_dir):
                        if filename.endswith(('.vtt', '.srt', '.json3')):
                            file_path = os.path.join(temp_dir, filename)
                            
                            # Parse the subtitle file
                            transcript_data = self._parse_subtitle_file(file_path)
                            if transcript_data:
                                return {
                                    "transcript": transcript_data,
                                    "text": self._format_transcript(transcript_data),
                                    "language": "English",
                                    "type": "auto"
                                }
                
        except Exception as e:
            logger.warning(f"yt-dlp method failed: {e}")
        
        return None
    
    def _parse_subtitle_file(self, file_path: str) -> List[Dict]:
        """Parse subtitle file (VTT, SRT, or JSON3 format)"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if file_path.endswith('.vtt'):
                return self._parse_vtt(content)
            elif file_path.endswith('.srt'):
                return self._parse_srt(content)
            elif file_path.endswith('.json3'):
                return self._parse_json3(content)
            
        except Exception as e:
            logger.warning(f"Failed to parse subtitle file: {e}")
        
        return []
    
    def _parse_vtt(self, content: str) -> List[Dict]:
        """Parse WebVTT format"""
        transcript_data = []
        
        # Skip header
        lines = content.strip().split('\n')
        i = 0
        while i < len(lines) and not lines[i].strip().startswith('00:'):
            i += 1
        
        while i < len(lines):
            line = lines[i].strip()
            
            # Check for timestamp
            if '-->' in line:
                # Parse timestamp
                parts = line.split('-->')
                start_time = self._parse_vtt_timestamp(parts[0].strip())
                
                # Get text
                i += 1
                text_lines = []
                while i < len(lines) and lines[i].strip() and '-->' not in lines[i]:
                    text_lines.append(lines[i].strip())
                    i += 1
                
                if text_lines:
                    transcript_data.append({
                        'text': ' '.join(text_lines),
                        'start': start_time,
                        'duration': 0  # VTT doesn't provide duration directly
                    })
            else:
                i += 1
        
        return transcript_data
    
    def _parse_vtt_timestamp(self, timestamp: str) -> float:
        """Convert VTT timestamp to seconds"""
        parts = timestamp.split(':')
        if len(parts) == 3:
            hours, minutes, seconds = parts
            return float(hours) * 3600 + float(minutes) * 60 + float(seconds)
        elif len(parts) == 2:
            minutes, seconds = parts
            return float(minutes) * 60 + float(seconds)
        return 0
    
    def _parse_srt(self, content: str) -> List[Dict]:
        """Parse SRT format"""
        # Similar to VTT but with different timestamp format
        # Implementation would be similar to _parse_vtt
        return []
    
    def _parse_json3(self, content: str) -> List[Dict]:
        """Parse JSON3 format"""
        try:
            data = json.loads(content)
            return self._parse_json_transcript(data)
        except:
            return []
    
    def _try_web_scraping(self, video_id: str) -> Optional[Dict]:
        """Last resort: try web scraping"""
        # This would involve more complex scraping logic
        # For now, return None
        return None
    
    def _format_transcript(self, transcript_data: List[Dict]) -> str:
        """Format transcript data as plain text"""
        return ' '.join(entry['text'] for entry in transcript_data)
    
    def __del__(self):
        """Clean up HTTP session"""
        if hasattr(self, 'session'):
            self.session.close()


# Singleton instance
transcript_extractor = YouTubeTranscriptExtractor()