#!/usr/bin/env python3
"""
Add transcript_status column to videos table
"""

import sqlite3
import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.core.config import settings

def add_transcript_status_column():
    """Add transcript_status column to videos table if it doesn't exist"""
    
    # Get database path
    db_path = settings.DATABASE_URL.replace("sqlite:///", "")
    
    if not os.path.exists(db_path):
        print(f"Database not found at {db_path}")
        return
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if column already exists
        cursor.execute("PRAGMA table_info(videos)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'transcript_status' not in columns:
            print("Adding transcript_status column...")
            cursor.execute("ALTER TABLE videos ADD COLUMN transcript_status VARCHAR(500)")
            conn.commit()
            print("✅ transcript_status column added successfully!")
        else:
            print("✅ transcript_status column already exists")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    add_transcript_status_column()