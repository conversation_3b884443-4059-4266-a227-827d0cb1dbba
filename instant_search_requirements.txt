# Requirements for Instant Video Search

# Core dependencies for embedding-based search
sentence-transformers>=2.2.2
chromadb>=0.4.0
torch>=2.0.0
numpy>=1.24.0

# Optional: For local object detection (YOLO)
ultralytics>=8.0.0

# Optional: For CLIP-based search
# Install with: pip install git+https://github.com/openai/CLIP.git
# clip-by-openai

# Optional: For faster image processing
Pillow>=9.0.0

# Already in your requirements.txt:
# redis
# opencv-python